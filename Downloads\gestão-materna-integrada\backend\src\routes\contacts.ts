import express, { Request, Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { contactService } from '../services/contactService';
import { authenticate, authorize, auditLog } from '../middleware/auth';

const router = express.Router();

// Validações simplificadas (apenas 3 campos)
const contactValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Nome deve ter entre 2 e 100 caracteres'),
  body('phone')
    .trim()
    .matches(/^\+?[\d\s\-\(\)]+$/)
    .withMessage('Formato de telefone inválido'),
  body('baby_gender')
    .optional()
    .isIn(['male', 'female', 'unknown'])
    .withMessage('Gênero do bebê inválido')
];

// GET /api/contacts - Listar contatos com filtros e paginação
router.get('/',
  authenticate,
  authorize('read:contacts'),
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Página deve ser um número positivo'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite deve ser entre 1 e 100'),
    query('search').optional().isLength({ max: 100 }).withMessage('Busca deve ter no máximo 100 caracteres'),
    query('babyGender').optional().isIn(['male', 'female', 'unknown']),
    query('sortBy').optional().isIn(['name', 'lastInteraction', 'createdAt']),
    query('sortOrder').optional().isIn(['asc', 'desc'])
  ],
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Parâmetros inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const search = req.query.search as string;
      const babyGender = req.query.babyGender as string;
      const sortBy = req.query.sortBy as string || 'lastInteraction';
      const sortOrder = req.query.sortOrder as string || 'desc';

      // Construir filtros
      const filters: any = { isActive: true };

      // Filtro por usuário (simplificado para Supabase)
      // TODO: Implementar filtro por usuário quando necessário

      if (search) {
        filters.$or = [
          { name: { $regex: search, $options: 'i' } },
          { phone: { $regex: search, $options: 'i' } }
        ];
      }

      if (babyGender) filters.babyGender = babyGender;

      // Executar consulta
      const skip = (page - 1) * limit;
      const sortOptions: any = {};
      sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

      // Usar contactService do Supabase
      const offset = (page - 1) * limit;
      const [contacts, total] = await Promise.all([
        contactService.findAll({
          isActive: true,
          limit,
          offset,
          search
        }),
        contactService.count({ isActive: true })
      ]);

      // Dados simplificados
      const enrichedContacts = contacts;

      res.json({
        contacts: enrichedContacts,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        filters: {
          search,
          babyGender
        }
      });
    } catch (error) {
      console.error('Erro ao listar contatos:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// GET /api/contacts/:id - Obter contato específico
router.get('/:id',
  authenticate,
  authorize('read:contacts'),
  async (req, res) => {
    try {
      const contact = await contactService.findById(req.params.id);

      if (!contact) {
        return res.status(404).json({
          error: 'Contato não encontrado',
          code: 'CONTACT_NOT_FOUND'
        });
      }

      res.json({
        contact,
        messageStats: {
          totalMessages: 0,
          lastMessage: null,
          fromMeCount: 0,
          fromContactCount: 0
        }
      });
    } catch (error) {
      console.error('Erro ao obter contato:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// POST /api/contacts - Criar novo contato
router.post('/',
  authenticate,
  authorize('write:contacts'),
  contactValidation,
  auditLog('CREATE_CONTACT'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      // Verificar se telefone já existe
      const existingContact = await contactService.findByPhone(req.body.phone);

      if (existingContact) {
        return res.status(409).json({
          error: 'Telefone já cadastrado',
          code: 'PHONE_ALREADY_EXISTS'
        });
      }

      // Criar contato usando Supabase
      const contactData = {
        name: req.body.name,
        phone: req.body.phone,
        baby_gender: req.body.baby_gender || 'unknown',
        registration_status: 'registered' as 'unregistered' | 'registered' | 'not_interested',
        is_active: true,
        last_interaction: new Date().toISOString(),
        evaluation_messages: 0,
        interest_score: 0
      };

      const contact = await contactService.create(contactData);

      res.status(201).json({
        message: 'Contato criado com sucesso',
        contact
      });
    } catch (error) {
      console.error('Erro ao criar contato:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// PUT /api/contacts/:id - Atualizar contato
router.put('/:id',
  authenticate,
  authorize('write:contacts'),
  contactValidation,
  auditLog('UPDATE_CONTACT'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const contact = await contactService.findById(req.params.id);

      if (!contact) {
        return res.status(404).json({
          error: 'Contato não encontrado',
          code: 'CONTACT_NOT_FOUND'
        });
      }

      // Verificar se telefone já existe (se alterado)
      if (req.body.phone && req.body.phone !== contact.phone) {
        const existingContact = await contactService.findByPhone(req.body.phone);

        if (existingContact && existingContact.id !== contact.id) {
          return res.status(409).json({
            error: 'Telefone já cadastrado',
            code: 'PHONE_ALREADY_EXISTS'
          });
        }
      }

      // Atualizar contato usando Supabase
      const updateData = {
        name: req.body.name || contact.name,
        phone: req.body.phone || contact.phone,
        baby_gender: req.body.baby_gender || contact.baby_gender
      };

      const updatedContact = await contactService.update(contact.id!, updateData);

      res.json({
        message: 'Contato atualizado com sucesso',
        contact: updatedContact
      });
    } catch (error) {
      console.error('Erro ao atualizar contato:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// DELETE /api/contacts/:id - Excluir contato (soft delete)
router.delete('/:id',
  authenticate,
  authorize('delete:contacts'),
  auditLog('DELETE_CONTACT'),
  async (req: Request, res: Response) => {
    try {
      const contact = await contactService.findById(req.params.id);

      if (!contact) {
        return res.status(404).json({
          error: 'Contato não encontrado',
          code: 'CONTACT_NOT_FOUND'
        });
      }

      // Soft delete usando Supabase
      await contactService.update(contact.id!, { is_active: false });

      res.json({
        message: 'Contato excluído com sucesso'
      });
    } catch (error) {
      console.error('Erro ao excluir contato:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// GET /api/contacts/:id/messages - Obter mensagens do contato (simplificado)
router.get('/:id/messages',
  authenticate,
  authorize('read:messages'),
  async (req: Request, res: Response) => {
    try {
      const contact = await contactService.findById(req.params.id);

      if (!contact) {
        return res.status(404).json({
          error: 'Contato não encontrado',
          code: 'CONTACT_NOT_FOUND'
        });
      }

      // Retornar array vazio por enquanto (mensagens não implementadas no Supabase)
      res.json({
        messages: [],
        pagination: {
          page: 1,
          limit: 50,
          total: 0,
          pages: 0
        }
      });
    } catch (error) {
      console.error('Erro ao obter mensagens:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// GET /api/contacts/stats - Estatísticas dos contatos (simplificado)
router.get('/stats/overview',
  authenticate,
  authorize('read:analytics'),
  async (req, res) => {
    try {
      // Usar contactService para obter estatísticas básicas
      const total = await contactService.count({ isActive: true });

      // Estatísticas simplificadas
      const result = {
        total,
        male: 0,
        female: 0,
        unknown: 0
      };

      res.json(result);
    } catch (error) {
      console.error('Erro ao obter estatísticas:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// ROTAS TEMPORÁRIAS PARA TESTE (SEM AUTENTICAÇÃO)

// POST /api/contacts/test - Criar contato sem autenticação (TEMPORÁRIO)
router.post('/test',
  contactValidation,
  async (req: Request, res: Response) => {
    try {
      console.log('📝 Tentativa de criar contato (teste):', req.body);

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log('❌ Erros de validação:', errors.array());
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      // Verificar se telefone já existe
      const existingContact = await contactService.findByPhone(req.body.phone);

      if (existingContact) {
        console.log('❌ Telefone já existe:', req.body.phone);
        return res.status(409).json({
          error: 'Telefone já cadastrado',
          code: 'PHONE_ALREADY_EXISTS'
        });
      }

      // Criar contato usando Supabase
      const contactData = {
        name: req.body.name,
        phone: req.body.phone,
        baby_gender: req.body.baby_gender || 'unknown',
        registration_status: 'registered' as 'unregistered' | 'registered' | 'not_interested',
        is_active: true,
        last_interaction: new Date().toISOString(),
        evaluation_messages: 0,
        interest_score: 0
      };

      console.log('📝 Dados do contato a ser criado:', contactData);

      const contact = await contactService.create(contactData);

      console.log('✅ Contato criado com sucesso:', contact);

      res.status(201).json({
        message: 'Contato criado com sucesso',
        contact
      });
    } catch (error) {
      console.error('❌ Erro ao criar contato:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR',
        details: (error as any).message
      });
    }
  }
);

// GET /api/contacts/test - Listar contatos sem autenticação (TEMPORÁRIO)
router.get('/test',
  async (req: Request, res: Response) => {
    try {
      console.log('📋 Listando contatos (teste)');

      const contacts = await contactService.findAll({
        isActive: true,
        limit: 50
      });

      console.log('📋 Contatos encontrados:', contacts.length);

      res.json({
        contacts,
        total: contacts.length
      });
    } catch (error) {
      console.error('❌ Erro ao listar contatos:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// Funções auxiliares removidas - não são mais necessárias no modelo simplificado

export default router;
