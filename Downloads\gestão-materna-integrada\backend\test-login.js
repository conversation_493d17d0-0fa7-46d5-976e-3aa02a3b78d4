const axios = require('axios');

async function testLogin() {
  try {
    console.log('🔧 Testando login...');

    const loginData = {
      email: '<EMAIL>',
      password: 'Admin123!@#'
    };

    console.log('📧 Email:', loginData.email);
    console.log('🔑 Senha:', loginData.password);
    console.log('🌐 URL:', 'http://localhost:3333/api/auth/login');

    // Testar se o servidor está respondendo
    console.log('🔍 Testando conectividade...');
    const healthCheck = await axios.get('http://localhost:3333/api/auth/login', {
      validateStatus: () => true // Aceitar qualquer status
    });
    console.log('📡 Servidor responde:', healthCheck.status);

    const response = await axios.post('http://localhost:3333/api/auth/login', loginData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 5000
    });

    console.log('✅ Login bem-sucedido!');
    console.log('📊 Status:', response.status);
    console.log('🎯 Resposta:', JSON.stringify(response.data, null, 2));

    // Testar se o token funciona
    console.log('🔐 Testando token...');
    const meResponse = await axios.get('http://localhost:3333/api/auth/me', {
      headers: {
        'Authorization': `Bearer ${response.data.token}`
      }
    });
    console.log('👤 Dados do usuário:', JSON.stringify(meResponse.data, null, 2));

  } catch (error) {
    console.error('❌ Erro no login:');
    console.error('📊 Status:', error.response?.status);
    console.error('📝 Mensagem:', error.response?.data || error.message);
    console.error('🔍 Detalhes:', error.response?.data?.details || 'Nenhum detalhe adicional');
    console.error('🌐 URL tentada:', error.config?.url);
    console.error('📡 Método:', error.config?.method);
  }
}

testLogin();
