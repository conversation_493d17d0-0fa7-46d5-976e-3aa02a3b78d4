import express, { Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
// MongoDB removido - usando Supabase
// import { User, IUser } from '../models/User';
import { userService } from '../services/userService';
import { authenticate, requireAdmin, auditLog } from '../middleware/auth';
import rateLimit from 'express-rate-limit';

const router = express.Router();

// Rate limiting para rotas de autenticação
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 5, // máximo 5 tentativas por IP
  message: {
    error: 'Muitas tentativas de login. Tente novamente em 15 minutos.',
    code: 'TOO_MANY_ATTEMPTS'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Validações
const registerValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Nome deve ter entre 2 e 100 caracteres'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email inválido'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Senha deve ter pelo menos 8 caracteres')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Senha deve conter pelo menos: 1 letra minúscula, 1 maiúscula, 1 número e 1 caractere especial'),
  body('role')
    .optional()
    .isIn(['admin', 'nurse', 'doctor', 'coordinator'])
    .withMessage('Role inválido')
];

const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email inválido'),
  body('password')
    .notEmpty()
    .withMessage('Senha é obrigatória')
];

// POST /api/auth/register - Registrar novo usuário
router.post('/register', 
  requireAdmin,
  registerValidation,
  auditLog('USER_REGISTER'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const { name, email, password, role } = req.body;

      // Verificar se usuário já existe
      const existingUser = await userService.findByEmail(email);
      if (existingUser) {
        return res.status(409).json({
          error: 'Email já está em uso',
          code: 'EMAIL_ALREADY_EXISTS'
        });
      }

      // Criar usuário usando Supabase
      const newUser = await userService.create({
        name,
        email,
        password,
        role: role || 'nurse',
        is_active: true,
        permissions: []
      });

      res.status(201).json({
        message: 'Usuário criado com sucesso',
        user: {
          id: newUser.id,
          name: newUser.name,
          email: newUser.email,
          role: newUser.role,
          permissions: newUser.permissions,
          createdAt: newUser.created_at
        }
      });
    } catch (error: any) {
      console.error('Erro ao registrar usuário:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// POST /api/auth/login - Login
router.post('/login',
  authLimiter,
  loginValidation,
  auditLog('USER_LOGIN'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const { email, password } = req.body;

      // Buscar usuário e verificar credenciais usando Supabase
      const authResult = await userService.authenticate(email, password);

      if (!authResult) {
        return res.status(401).json({
          error: 'Credenciais inválidas',
          code: 'INVALID_CREDENTIALS'
        });
      }

      res.json({
        message: 'Login realizado com sucesso',
        token: authResult.token,
        user: {
          id: authResult.user.id,
          name: authResult.user.name,
          email: authResult.user.email,
          role: authResult.user.role,
          permissions: authResult.user.permissions,
          lastLogin: authResult.user.last_login
        }
      });
    } catch (error: any) {
      console.error('Erro ao fazer login:', error);

      if (error.message === 'Credenciais inválidas') {
        return res.status(401).json({
          error: 'Email ou senha incorretos',
          code: 'INVALID_CREDENTIALS'
        });
      }

      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// GET /api/auth/me - Obter dados do usuário atual
router.get('/me', authenticate, async (req: Request, res: Response) => {
  try {
    // TODO: Implementar userService.findById para Supabase
    res.json({
      user: {
        id: (req.user as any).id || (req.user as any)._id,
        name: req.user!.name,
        email: req.user!.email,
        role: req.user!.role,
        permissions: req.user!.permissions,
        lastLogin: req.user!.lastLogin || (req.user as any).last_login,
        createdAt: req.user!.createdAt || (req.user as any).created_at
      }
    });
  } catch (error) {
    console.error('Erro ao obter dados do usuário:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      code: 'INTERNAL_ERROR'
    });
  }
});

// PUT /api/auth/profile - Atualizar perfil do usuário
router.put('/profile',
  authenticate,
  [
    body('name')
      .optional()
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Nome deve ter entre 2 e 100 caracteres'),
    body('email')
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage('Email inválido')
  ],
  auditLog('PROFILE_UPDATE'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      const { name, email } = req.body;
      const updates: any = {};

      // TODO: Implementar userService.update para Supabase
      return res.status(501).json({
        error: 'Funcionalidade em desenvolvimento - Use userService.update do Supabase',
        code: 'NOT_IMPLEMENTED'
      });

      // Código removido - não será executado devido ao return acima
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// PUT /api/auth/password - Alterar senha
router.put('/password',
  authenticate,
  [
    body('currentPassword')
      .notEmpty()
      .withMessage('Senha atual é obrigatória'),
    body('newPassword')
      .isLength({ min: 8 })
      .withMessage('Nova senha deve ter pelo menos 8 caracteres')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Nova senha deve conter pelo menos: 1 letra minúscula, 1 maiúscula, 1 número e 1 caractere especial')
  ],
  auditLog('PASSWORD_CHANGE'),
  async (req: Request, res: Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: errors.array()
        });
      }

      // TODO: Implementar userService.changePassword para Supabase
      return res.status(501).json({
        error: 'Funcionalidade em desenvolvimento - Use userService.changePassword do Supabase',
        code: 'NOT_IMPLEMENTED'
      });

      res.json({
        message: 'Senha alterada com sucesso'
      });
    } catch (error) {
      console.error('Erro ao alterar senha:', error);
      res.status(500).json({
        error: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

// POST /api/auth/logout - Logout (invalidar token no frontend)
router.post('/logout', authenticate, auditLog('USER_LOGOUT'), (req, res) => {
  res.json({
    message: 'Logout realizado com sucesso'
  });
});

// POST /api/auth/create-test-admin - Criar usuário admin de teste (rota temporária)
router.post('/create-test-admin', async (req: Request, res: Response) => {
  try {
    const testEmail = '<EMAIL>';

    // Verificar se já existe
    const existingUser = await userService.findByEmail(testEmail);
    if (existingUser) {
      return res.status(409).json({
        error: 'Usuário de teste já existe',
        code: 'USER_EXISTS'
      });
    }

    // Criar usuário admin de teste
    const adminUser = await userService.create({
      name: 'Admin Teste',
      email: testEmail,
      password: 'test123',
      role: 'admin',
      is_active: true,
      permissions: ['all']
    });

    res.status(201).json({
      message: 'Usuário admin de teste criado com sucesso',
      credentials: {
        email: testEmail,
        password: 'test123'
      },
      user: {
        id: adminUser.id,
        name: adminUser.name,
        email: adminUser.email,
        role: adminUser.role
      }
    });
  } catch (error: any) {
    console.error('Erro ao criar admin de teste:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      code: 'INTERNAL_ERROR'
    });
  }
});

export default router;
