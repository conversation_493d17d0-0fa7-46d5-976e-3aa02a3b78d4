import { webhookService, SystemWebhookEvents } from './webhookService';

/**
 * Serviço centralizado para emissão de eventos do sistema
 * Facilita a emissão de webhooks para todos os eventos importantes
 */
export class SystemEventEmitter {
  private static instance: SystemEventEmitter;

  private constructor() {}

  public static getInstance(): SystemEventEmitter {
    if (!SystemEventEmitter.instance) {
      SystemEventEmitter.instance = new SystemEventEmitter();
    }
    return SystemEventEmitter.instance;
  }

  // Eventos de Contato
  public emitContactCreated(contactData: any): void {
    this.emitEvent(SystemWebhookEvents.CONTACT_CREATED, {
      contact: contactData,
      action: 'created'
    }, 'system', 'medium');
  }

  public emitContactUpdated(contactId: string, changes: any): void {
    this.emitEvent(SystemWebhookEvents.CONTACT_UPDATED, {
      contact_id: contactId,
      changes,
      action: 'updated'
    }, 'system', 'low');
  }

  public emitContactDeleted(contactId: string): void {
    this.emitEvent(SystemWebhookEvents.CONTACT_DELETED, {
      contact_id: contactId,
      action: 'deleted'
    }, 'system', 'medium');
  }

  // Eventos de Agendamento
  public emitScheduleCreated(scheduleData: any): void {
    this.emitEvent(SystemWebhookEvents.SCHEDULE_CREATED, {
      schedule: scheduleData,
      action: 'created'
    }, 'scheduler', 'medium');
  }

  public emitScheduleExecuted(scheduleId: string, result: any): void {
    this.emitEvent(SystemWebhookEvents.SCHEDULE_EXECUTED, {
      schedule_id: scheduleId,
      result,
      action: 'executed'
    }, 'scheduler', 'low');
  }

  // Eventos de IA
  public emitAIResponseGenerated(contactId: string, message: string, response: any): void {
    this.emitEvent(SystemWebhookEvents.AI_RESPONSE_GENERATED, {
      contact_id: contactId,
      original_message: message,
      ai_response: response,
      action: 'ai_response'
    }, 'ai', 'low');
  }

  public emitSentimentAnalyzed(contactId: string, analysis: any): void {
    this.emitEvent(SystemWebhookEvents.SENTIMENT_ANALYZED, {
      contact_id: contactId,
      analysis,
      action: 'sentiment_analysis'
    }, 'ai', analysis.priority === 'alta' ? 'high' : 'low');
  }

  // Eventos de Sistema
  public emitSystemStartup(serverInfo: any): void {
    this.emitEvent(SystemWebhookEvents.SYSTEM_STARTUP, {
      server_info: serverInfo,
      startup_time: new Date(),
      action: 'startup'
    }, 'system', 'medium');
  }

  public emitSystemShutdown(reason?: string): void {
    this.emitEvent(SystemWebhookEvents.SYSTEM_SHUTDOWN, {
      reason: reason || 'Normal shutdown',
      shutdown_time: new Date(),
      action: 'shutdown'
    }, 'system', 'high');
  }

  public emitBulkMessageCompleted(stats: any): void {
    this.emitEvent(SystemWebhookEvents.BULK_MESSAGE_COMPLETED, {
      stats,
      completion_time: new Date(),
      action: 'bulk_completed'
    }, 'whatsapp', 'medium');
  }

  public emitTemplateUsed(templateId: string, contactId: string): void {
    this.emitEvent(SystemWebhookEvents.TEMPLATE_USED, {
      template_id: templateId,
      contact_id: contactId,
      used_at: new Date(),
      action: 'template_used'
    }, 'system', 'low');
  }

  // Eventos de Autenticação
  public emitUserLogin(userId: string, userInfo: any): void {
    this.emitEvent(SystemWebhookEvents.USER_LOGIN, {
      user_id: userId,
      user_info: userInfo,
      login_time: new Date(),
      action: 'login'
    }, 'auth', 'low');
  }

  public emitUserLogout(userId: string): void {
    this.emitEvent(SystemWebhookEvents.USER_LOGOUT, {
      user_id: userId,
      logout_time: new Date(),
      action: 'logout'
    }, 'auth', 'low');
  }

  // Eventos de Dados
  public emitDataExport(userId: string, exportType: string, recordCount: number): void {
    this.emitEvent(SystemWebhookEvents.DATA_EXPORT, {
      user_id: userId,
      export_type: exportType,
      record_count: recordCount,
      export_time: new Date(),
      action: 'data_export'
    }, 'data', 'medium');
  }

  public emitDataImport(userId: string, importType: string, stats: any): void {
    this.emitEvent(SystemWebhookEvents.DATA_IMPORT, {
      user_id: userId,
      import_type: importType,
      stats,
      import_time: new Date(),
      action: 'data_import'
    }, 'data', 'medium');
  }

  // Eventos de Saúde
  public emitHealthCheckFailed(checkId: string, error: any): void {
    this.emitEvent(SystemWebhookEvents.HEALTH_CHECK_FAILED, {
      check_id: checkId,
      error,
      failed_at: new Date(),
      action: 'health_check_failed'
    }, 'health', 'high');
  }

  public emitResourceWarning(resource: string, details: any): void {
    this.emitEvent(SystemWebhookEvents.RESOURCE_WARNING, {
      resource,
      details,
      warning_time: new Date(),
      action: 'resource_warning'
    }, 'health', 'high');
  }

  // Método privado para emitir eventos
  private emitEvent(
    type: string, 
    data: any, 
    source: 'whatsapp' | 'system' | 'ai' | 'scheduler' | 'health' | 'auth' | 'data',
    priority: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): void {
    try {
      const event = {
        id: `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type,
        timestamp: new Date(),
        data,
        source,
        priority
      };

      // webhookService.emitWebhookEvent(event); // Desabilitado temporariamente
      
      // Log baseado na prioridade
      if (priority === 'critical' || priority === 'high') {
        console.warn(`🚨 Evento ${priority.toUpperCase()}: ${type}`, data);
      } else {
        console.log(`📡 Evento emitido: ${type}`, { source, priority });
      }
    } catch (error) {
      console.error('❌ Erro ao emitir evento:', error);
    }
  }
}

// Instância singleton para uso global
export const systemEventEmitter = SystemEventEmitter.getInstance();

// Exportar eventos para facilitar uso
export { SystemWebhookEvents };
