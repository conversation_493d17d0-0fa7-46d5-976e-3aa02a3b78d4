import axios from 'axios';

async function createAdmin() {
  try {
    console.log('🔧 Criando usuário admin...');
    
    const adminData = {
      name: 'Administrador',
      email: '<EMAIL>',
      password: 'Admin123!@#',
      role: 'admin',
      phone: '+5511999999999'
    };
    
    const response = await axios.post('http://localhost:3333/api/auth/register', adminData);
    
    console.log('✅ Usuário admin criado com sucesso!');
    console.log('📧 Email:', adminData.email);
    console.log('🔑 Senha:', adminData.password);
    console.log('👤 Role:', adminData.role);
    
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.message?.includes('já existe')) {
      console.log('ℹ️  Usuário admin já existe!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Senha: Admin123!@#');
    } else {
      console.error('❌ Erro ao criar usuário:', error.response?.data || error.message);
    }
  }
}

createAdmin();
