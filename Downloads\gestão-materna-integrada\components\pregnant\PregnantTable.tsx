import React from 'react';
import type { Contact } from '../../src/services/api';
import { formatDate } from '../../src/utils/dateFormatter';
import Button from '../shared/Button';
import { ICONS } from '../../constants';
import { formatBrazilianPhoneNumberForDisplay } from '../../src/utils/phoneUtils';

interface PregnantTableProps {
  contacts: Contact[];
  onEdit: (contact: Contact) => void;
  onDelete: (contact: Contact) => void;
  onSendMessage: (contact: Contact, message: string) => Promise<void>;
  onGenerateFollowUp: (contact: Contact) => Promise<void>;
}

const PregnantTable: React.FC<PregnantTableProps> = ({
  contacts,
  onEdit,
  onDelete,
  onSendMessage,
  onGenerateFollowUp
}) => {
  // Garantir que contacts seja sempre um array
  const safeContacts = Array.isArray(contacts) ? contacts : [];

  if (safeContacts.length === 0) {
    return <p className="text-center text-gray-500 py-8">Nenhuma gestante encontrada.</p>;
  }

  return (
    <div className="bg-white shadow-md rounded-lg overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nome</th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Telefone</th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gênero do Bebê</th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Última Interação</th>
            <th scope="col" className="relative px-6 py-3">
              <span className="sr-only">Ações</span>
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {safeContacts.map((contact) => (
            <tr key={contact.id || contact._id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm font-medium text-neutral-dark">{contact.name}</div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900">{formatBrazilianPhoneNumberForDisplay(contact.phone)}</div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900 flex items-center gap-2">
                  {(() => {
                    const gender = contact.babyGender || contact.baby_gender;
                    if (gender === 'male') {
                      return (
                        <>
                          <span className="text-blue-500">💙</span>
                          <span>Menino</span>
                        </>
                      );
                    } else if (gender === 'female') {
                      return (
                        <>
                          <span className="text-pink-500">💗</span>
                          <span>Menina</span>
                        </>
                      );
                    } else {
                      return (
                        <>
                          <span>❓</span>
                          <span>Não informado</span>
                        </>
                      );
                    }
                  })()}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900">
                  {(() => {
                    const lastInteraction = contact.lastInteraction || contact.last_interaction;
                    return lastInteraction ? formatDate(lastInteraction) : 'Nunca';
                  })()}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex space-x-2 justify-end">
                  <Button variant="ghost" size="sm" onClick={() => onEdit(contact)} title="Editar">
                    {ICONS.edit}
                  </Button>
                  <Button variant="ghost" size="sm" onClick={() => onDelete(contact)} className="text-red-600 hover:text-red-700 hover:bg-red-50" title="Excluir">
                    {ICONS.delete}
                  </Button>
                  <Button variant="ghost" size="sm" onClick={() => onGenerateFollowUp(contact)} className="text-green-600 hover:text-green-700 hover:bg-green-50" title="Acompanhamento">
                    {ICONS.followUp}
                  </Button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default PregnantTable;
    