const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Configuração do Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variáveis SUPABASE_URL e SUPABASE_ANON_KEY são obrigatórias');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function createAdminSimple() {
  try {
    console.log('🔧 Criando usuário admin no Supabase...');
    
    // Hash da senha
    const hashedPassword = await bcrypt.hash('Admin123!@#', 10);
    
    const adminData = {
      name: 'Administrador',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      is_active: true,
      permissions: ['read:all', 'write:all', 'delete:all', 'admin:all']
    };
    
    // Verificar se usuário já existe
    const { data: existingUser } = await supabase
      .from('users')
      .select('*')
      .eq('email', adminData.email)
      .single();
    
    if (existingUser) {
      console.log('ℹ️  Usuário admin já existe!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Senha: Admin123!@#');
      console.log('👤 Role:', existingUser.role);
      return;
    }
    
    // Criar usuário
    const { data, error } = await supabase
      .from('users')
      .insert([adminData])
      .select();
    
    if (error) {
      console.error('❌ Erro ao criar usuário:', error);
      return;
    }
    
    console.log('✅ Usuário admin criado com sucesso!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Senha: Admin123!@#');
    console.log('👤 Role: admin');
    console.log('📊 ID:', data[0].id);
    
  } catch (error) {
    console.error('❌ Erro:', error.message);
  }
}

createAdminSimple();
