import { GoogleGenerativeAI, FileState, GoogleAIFileManager } from '@google/generative-ai';
import { Message, IMessage } from '../models/Message';
import { IContact } from '../models/Contact';
import { aiEventMiddleware } from '../middleware/eventCapture';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

// NOVO: Interface para dados de áudio
interface AudioData {
  buffer: Buffer;
  mimetype: string;
}

// NOVO: Função para criar arquivo temporário e fazer upload para Gemini
async function uploadAudioToGemini(ai: GoogleGenerativeAI, buffer: Buffer, mimeType: string) {
  // Criar arquivo temporário
  const tempDir = os.tmpdir();
  const fileExtension = mimeType.includes('webm') ? '.webm' :
                       mimeType.includes('mp3') ? '.mp3' :
                       mimeType.includes('wav') ? '.wav' :
                       mimeType.includes('ogg') ? '.ogg' : '.audio';

  const tempFilePath = path.join(tempDir, `audio_${Date.now()}${fileExtension}`);

  try {
    // Escrever buffer para arquivo temporário
    fs.writeFileSync(tempFilePath, buffer);

    console.log('🎵 Fazendo upload do áudio para Gemini:', {
      filePath: tempFilePath,
      mimeType,
      size: buffer.length
    });

    // Upload para Gemini usando GoogleAIFileManager
    const fileManager = new GoogleAIFileManager(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || '');
    const uploadResult = await fileManager.uploadFile(tempFilePath, {
      mimeType,
      displayName: `Audio_${Date.now()}`
    });

    console.log('✅ Upload concluído:', uploadResult.file.name);

    // Aguardar processamento se necessário
    let file = uploadResult.file;
    while (file.state === FileState.PROCESSING) {
      console.log('⏳ Aguardando processamento do áudio...');
      await new Promise(resolve => setTimeout(resolve, 1000));
      file = await fileManager.getFile(file.name);
    }

    if (file.state === FileState.FAILED) {
      throw new Error('Falha no processamento do áudio pelo Gemini');
    }

    console.log('🎵 Áudio processado com sucesso pelo Gemini');
    return file;

  } finally {
    // Limpar arquivo temporário
    try {
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
        console.log('🗑️ Arquivo temporário removido:', tempFilePath);
      }
    } catch (error) {
      console.warn('⚠️ Erro ao remover arquivo temporário:', error);
    }
  }
}

// Função avançada para limpar JSON retornado pelo Gemini
function cleanGeminiJSON(text: string, attempt: number = 1): string {
  try {
    console.log(`🧹 Tentativa ${attempt} - Limpando JSON do Gemini:`, text.substring(0, 100) + '...');

    let cleaned = text;

    // Estratégia 1: Remover markdown code blocks
    cleaned = cleaned.replace(/```json\s*/gi, '').replace(/```\s*/g, '');

    // Estratégia 2: Remover caracteres de controle e espaços extras
    cleaned = cleaned.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
    cleaned = cleaned.trim();

    // Estratégia 3: Remover texto antes e depois do JSON
    const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      cleaned = jsonMatch[0];
    }

    // Estratégia 4: Tentar encontrar JSON em array
    if (!cleaned.startsWith('{') && cleaned.includes('[')) {
      const arrayMatch = cleaned.match(/\[[\s\S]*\]/);
      if (arrayMatch) {
        cleaned = arrayMatch[0];
      }
    }

    // Estratégia 5: Limpar caracteres problemáticos comuns
    cleaned = cleaned
      .replace(/,\s*}/g, '}') // Remove vírgulas antes de }
      .replace(/,\s*]/g, ']') // Remove vírgulas antes de ]
      .replace(/\n\s*\n/g, '\n') // Remove linhas vazias duplas
      .replace(/\t/g, ' ') // Substitui tabs por espaços
      .replace(/\r/g, ''); // Remove carriage returns

    console.log(`✅ Tentativa ${attempt} - JSON limpo:`, cleaned.substring(0, 100) + '...');
    return cleaned;
  } catch (error) {
    console.error(`❌ Erro na tentativa ${attempt} ao limpar JSON do Gemini:`, error);
    return text;
  }
}

// Função para tentar múltiplas estratégias de parsing
function parseJSONWithFallback(text: string): any {
  const maxAttempts = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const cleaned = cleanGeminiJSON(text, attempt);

      // Tentar parse direto
      const parsed = JSON.parse(cleaned);
      console.log(`✅ JSON parseado com sucesso na tentativa ${attempt}`);
      return parsed;
    } catch (error) {
      lastError = error as Error;
      console.warn(`⚠️ Tentativa ${attempt} falhou:`, error);

      if (attempt < maxAttempts) {
        // Estratégias adicionais para tentativas subsequentes
        if (attempt === 2) {
          // Tentar com escape de caracteres especiais
          text = text.replace(/\\/g, '\\\\').replace(/"/g, '\\"');
        } else if (attempt === 3) {
          // Tentar extrair apenas números e strings básicas
          const simpleMatch = text.match(/\{[^{}]*\}/);
          if (simpleMatch) {
            text = simpleMatch[0];
          }
        }
      }
    }
  }

  // Se todas as tentativas falharam, logar detalhes completos
  console.error('❌ Todas as tentativas de parsing falharam');
  console.error('📄 Texto original completo:', text);
  console.error('📄 Último erro:', lastError?.message);

  throw new Error(`Falha ao fazer parse do JSON após ${maxAttempts} tentativas: ${lastError?.message}`);
}

// Função para validar e ajustar tamanho da resposta
function validateResponseLength(response: string, minWords: number = 15, maxWords: number = 60): string {
  const words = response.trim().split(/\s+/);
  const wordCount = words.length;

  console.log(`📏 Resposta da IA: ${wordCount} palavras (meta: ${minWords}-${maxWords})`);

  if (wordCount >= minWords && wordCount <= maxWords) {
    console.log('✅ Tamanho da resposta adequado');
    return response;
  }

  if (wordCount > maxWords) {
    // Truncar resposta se muito longa
    const truncated = words.slice(0, maxWords).join(' ');
    console.log(`✂️ Resposta truncada de ${wordCount} para ${maxWords} palavras`);
    return truncated + (truncated.endsWith('.') ? '' : '.');
  }

  if (wordCount < minWords) {
    console.log(`⚠️ Resposta muito curta (${wordCount} palavras), mantendo original`);
    return response;
  }

  return response;
}

export class GeminiAIService {
  private ai: GoogleGenerativeAI | null = null;
  private model: any = null;
  private isInitialized: boolean = false;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;

    if (!apiKey) {
      console.warn('⚠️  GEMINI_API_KEY não configurada. Serviço de IA desabilitado.');
      this.isInitialized = false;
      return;
    }

    try {
      this.ai = new GoogleGenerativeAI(apiKey);
      // ALTERADO: Usamos o modelo 1.5 Flash que suporta áudio, vídeo, etc.
      this.model = this.ai.getGenerativeModel({ model: "gemini-1.5-flash" });
      this.isInitialized = true;
      console.log('✅ Gemini AI inicializado com sucesso (modelo: gemini-1.5-flash)');

      // Iniciar limpeza automática de arquivos
      this.startAutoCleanup();
    } catch (error) {
      console.error('❌ Erro ao inicializar Gemini AI:', error);
      this.isInitialized = false;
    }
  }

  // NOVO: Iniciar limpeza automática
  private startAutoCleanup(): void {
    // Executar limpeza a cada hora (3600000 ms)
    setInterval(async () => {
      await this.cleanupOldFiles();
    }, 3600000);

    // Executar primeira limpeza após 5 minutos
    setTimeout(async () => {
      await this.cleanupOldFiles();
    }, 300000);

    console.log('🧹 Limpeza automática de arquivos de áudio configurada (a cada hora)');
  }

  // ALTERADO: Método agora aceita tanto string quanto array de partes (para suporte a áudio)
  public async generateContent(promptParts: string | any[]): Promise<string> {
    if (!this.isInitialized || !this.model) {
      throw new Error('Gemini AI não está inicializado');
    }

    try {
      const result = await this.model.generateContent(promptParts);
      const response = await result.response;
      return response.text() || '';
    } catch (error) {
      console.error('Erro ao gerar conteúdo:', error);
      throw error;
    }
  }

  private async getConversationContext(contact: IContact, limit: number = 10) {
    // Contexto de conversa seria implementado com Supabase
    // Por enquanto, retornar array vazio
    console.log('📝 Contexto de conversa não implementado para Supabase');
    return [];
  }

  private formatContext(messages: IMessage[]) {
    return messages.map(msg => ({
      role: msg.fromMe ? 'assistant' : 'user',
      content: msg.content
    }));
  }

  // ALTERADO: generateResponse agora aceita um parâmetro opcional 'audioData'
  public async generateResponse(contact: IContact, message: string, audioData?: AudioData) {
    try {
      const context = await this.getConversationContext(contact);
      const formattedContext = this.formatContext(context);

      const promptInstruction = `
        Você é "Rafaela", assistente virtual inspirada na Vereadora Rafaela de Parnamirim - fonoaudióloga, cristã e defensora da inclusão.

        PERSONALIDADE RAFAELA:
        🧡 Tom maternal e acolhedor como uma irmã mais velha
        ✊🏾 Lutadora incansável pelo bem-estar das gestantes
        🙏🏽 Fé cristã que traz esperança e força
        💪🏽 "Seguimos firmes juntas" - linguagem de união e força

        ESTILO DE COMUNICAÇÃO:
        - Use "minha querida", "nossa gente", "juntas somos mais fortes"
        - Emoji principal: 🧡 (quando apropriado)
        - Referências à fé: "Deus abençoa", "com fé e esperança"
        - Linguagem de ação: "seguimos firmes", "caminhando juntas"
        - Tom inclusivo e acessível

        Contexto da gestante:
        - Nome: ${contact.name}
        - Telefone: ${contact.phone}
        - Gênero do bebê: ${contact.babyGender === 'male' ? 'Menino' : contact.babyGender === 'female' ? 'Menina' : 'Não informado'}

        Histórico da conversa:
        ${formattedContext.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

        Nova mensagem da gestante: ${message}
        ${audioData ? '(IMPORTANTE: Há um áudio anexado. Ouça o áudio e responda principalmente baseado no que a gestante falou. Use o texto apenas como contexto adicional.)' : ''}

        DIRETRIZES (15-60 palavras):
        1. Tom maternal da Rafaela: "minha querida", "nossa família"
        2. Use o nome da gestante com carinho
        3. Mencione o bebê com gênero correto se conhecido
        4. Inclua elementos de fé quando apropriado
        5. Linguagem de força e união: "seguimos firmes", "juntas"
        6. Seja empática como a Vereadora Rafaela
        7. Sugira médico se necessário
        8. Termine com encorajamento

        RESPOSTA NO ESTILO RAFAELA (15-60 palavras):`;

      // Montamos as "partes" do prompt
      let promptParts: any[] = [promptInstruction];

      if (audioData && this.ai) {
        // NOVO: Upload nativo do áudio para Gemini
        console.log('🎵 Fazendo upload do áudio para Gemini (método nativo)');
        try {
          const uploadedFile = await uploadAudioToGemini(this.ai, audioData.buffer, audioData.mimetype);
          promptParts.push(uploadedFile);
          console.log('✅ Áudio adicionado ao prompt via upload nativo');
        } catch (uploadError) {
          console.error('❌ Erro no upload do áudio, usando método fallback:', uploadError);
          // Fallback para o método anterior se o upload falhar
          promptParts.push({
            inlineData: {
              data: audioData.buffer.toString("base64"),
              mimeType: audioData.mimetype,
            },
          });
        }
      }

      const rawResponse = await this.generateContent(promptParts);

      // Validar e ajustar tamanho da resposta (15-60 palavras)
      const response = validateResponseLength(rawResponse, 15, 60);

      // Análise de sentimento e necessidades
      const sentimentAnalysis = await this.analyzeSentiment(message, contact);

      const result = {
        response,
        sentiment: sentimentAnalysis.sentiment,
        needs: sentimentAnalysis.needs,
        suggestions: sentimentAnalysis.suggestions
      };

      // Emitir eventos para webhooks
      // aiEventMiddleware seria implementado com ID do Supabase
      console.log('📊 Event middleware não implementado para Supabase');

      return result;
    } catch (error) {
      console.error('Erro ao gerar resposta:', error);
      throw error;
    }
  }

  private async analyzeSentiment(message: string, contact: IContact) {
    const prompt = `
      Analise a seguinte mensagem de uma gestante e forneça uma análise detalhada em formato JSON:
      
      Contexto:
      - Nome: ${contact.name}
      - Estágio da gestação: Não informado
      
      Mensagem: ${message}
      
      Forneça uma análise com os seguintes campos:
      {
        "sentiment": {
          "type": "positive/negative/neutral",
          "score": 0.0 a 1.0,
          "emotions": ["lista de emoções identificadas"]
        },
        "needs": ["lista de necessidades identificadas"],
        "suggestions": ["sugestões de acompanhamento"],
        "priority": "alta/média/baixa",
        "medical_attention": true/false,
        "follow_up": ["ações de acompanhamento recomendadas"]
      }`;

    const result = await this.generateContent(prompt);

    try {
      // Usar função avançada de parsing com múltiplas tentativas
      return parseJSONWithFallback(result);
    } catch (parseError) {
      console.error('❌ Todas as tentativas de parse falharam:', parseError);
      console.error('📄 Resposta original do Gemini:', result);

      // Tentar análise manual básica antes do fallback
      const manualAnalysis = this.attemptManualAnalysis(result, message);
      if (manualAnalysis) {
        console.log('🔧 Análise manual bem-sucedida');
        return manualAnalysis;
      }

      // Retornar análise padrão apenas como último recurso
      console.warn('⚠️ Usando análise padrão como último recurso');
      return {
        sentiment: {
          type: 'neutral',
          score: 0.5,
          emotions: ['indefinido']
        },
        needs: ['análise automática falhou - revisar resposta da IA'],
        suggestions: ['verificar logs do sistema', 'revisar prompt da IA'],
        priority: 'média',
        medical_attention: false,
        follow_up: ['acompanhar evolução', 'revisar configuração da IA'],
        debug_info: {
          original_response: result.substring(0, 500),
          error: (parseError as any).message,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  // Método para tentar análise manual básica quando o JSON falha
  private attemptManualAnalysis(aiResponse: string, originalMessage: string): any | null {
    try {
      console.log('🔧 Tentando análise manual da resposta da IA');

      const response = aiResponse.toLowerCase();
      const message = originalMessage.toLowerCase();

      // Detectar sentimento básico por palavras-chave
      let sentimentType = 'neutral';
      let sentimentScore = 0.5;
      const emotions: string[] = [];

      // Palavras positivas
      const positiveWords = ['feliz', 'alegre', 'bem', 'ótimo', 'bom', 'tranquila', 'animada'];
      const negativeWords = ['triste', 'preocupada', 'dor', 'mal', 'ruim', 'medo', 'ansiedade'];
      const urgentWords = ['urgente', 'emergência', 'sangramento', 'dor forte', 'hospital'];

      if (positiveWords.some(word => message.includes(word))) {
        sentimentType = 'positive';
        sentimentScore = 0.7;
        emotions.push('alegria', 'tranquilidade');
      } else if (negativeWords.some(word => message.includes(word))) {
        sentimentType = 'negative';
        sentimentScore = 0.3;
        emotions.push('preocupação');
      }

      // Detectar necessidades básicas
      const needs: string[] = [];
      const suggestions: string[] = [];
      let priority = 'média';
      let medicalAttention = false;

      if (urgentWords.some(word => message.includes(word))) {
        needs.push('atendimento médico urgente');
        suggestions.push('entrar em contato com médico imediatamente');
        priority = 'alta';
        medicalAttention = true;
      } else if (message.includes('dúvida') || message.includes('pergunta')) {
        needs.push('esclarecimento');
        suggestions.push('fornecer informações educativas');
      } else {
        needs.push('acompanhamento de rotina');
        suggestions.push('manter contato regular');
      }

      return {
        sentiment: {
          type: sentimentType,
          score: sentimentScore,
          emotions
        },
        needs,
        suggestions,
        priority,
        medical_attention: medicalAttention,
        follow_up: ['acompanhar evolução'],
        analysis_method: 'manual_fallback'
      };
    } catch (error) {
      console.error('❌ Erro na análise manual:', error);
      return null;
    }
  }

  public async generateFollowUpMessage(contact: IContact) {
    const prompt = `
      Você é Rafaela, inspirada na Vereadora de Parnamirim. Gere uma mensagem de acompanhamento para uma gestante:

      PERSONALIDADE RAFAELA:
      🧡 Maternal, acolhedora e próxima da comunidade
      💪🏽 "Seguimos firmes juntas" - linguagem de força e união
      🙏🏽 Fé cristã que traz esperança

      Gestante:
      - Nome: ${contact.name}
      - Gênero do bebê: ${contact.babyGender === 'male' ? 'Menino' : contact.babyGender === 'female' ? 'Menina' : 'Não informado'}

      ESTILO RAFAELA (20-50 palavras):
      1. Use "minha querida", "nossa gente"
      2. Tom maternal: "como você está?"
      3. Mencione o bebê com carinho
      4. Inclua elementos de fé quando apropriado: "Deus abençoa"
      5. Linguagem de união: "seguimos juntas", "nossa família"
      6. Ofereça suporte: "estou aqui para você"
      7. Termine com força e esperança

      MENSAGEM NO ESTILO RAFAELA (20-50 palavras):`;

    const rawResult = await this.generateContent(prompt);

    // Validar e ajustar tamanho da mensagem de follow-up (20-50 palavras)
    const result = validateResponseLength(rawResult, 20, 50);
    return result;
  }

  // NOVO: Método para limpar arquivos antigos do Gemini
  public async cleanupOldFiles(): Promise<void> {
    if (!this.ai) return;

    try {
      console.log('🧹 Limpando arquivos antigos do Gemini...');

      const files = await this.ai.listFiles();
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000); // 1 hora atrás

      let deletedCount = 0;
      for (const file of files.files) {
        const fileDate = new Date(file.createTime);
        if (fileDate < oneHourAgo && file.displayName?.startsWith('Audio_')) {
          try {
            await this.ai.deleteFile(file.name);
            deletedCount++;
            console.log(`🗑️ Arquivo removido: ${file.name}`);
          } catch (deleteError) {
            console.warn(`⚠️ Erro ao remover arquivo ${file.name}:`, deleteError);
          }
        }
      }

      if (deletedCount > 0) {
        console.log(`✅ Limpeza concluída: ${deletedCount} arquivos removidos`);
      } else {
        console.log('✅ Nenhum arquivo antigo encontrado para remoção');
      }
    } catch (error) {
      console.error('❌ Erro na limpeza de arquivos:', error);
    }
  }
}
