import { GoogleGenerativeAI } from '@google/generative-ai';
import { Message, IMessage } from '../models/Message';
import { IContact } from '../models/Contact';
import { aiEventMiddleware } from '../middleware/eventCapture';

// Função avançada para limpar JSON retornado pelo Gemini
function cleanGeminiJSON(text: string, attempt: number = 1): string {
  try {
    console.log(`🧹 Tentativa ${attempt} - Limpando JSON do Gemini:`, text.substring(0, 100) + '...');

    let cleaned = text;

    // Estratégia 1: Remover markdown code blocks
    cleaned = cleaned.replace(/```json\s*/gi, '').replace(/```\s*/g, '');

    // Estratégia 2: Remover caracteres de controle e espaços extras
    cleaned = cleaned.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
    cleaned = cleaned.trim();

    // Estratégia 3: Remover texto antes e depois do JSON
    const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      cleaned = jsonMatch[0];
    }

    // Estratégia 4: Tentar encontrar JSON em array
    if (!cleaned.startsWith('{') && cleaned.includes('[')) {
      const arrayMatch = cleaned.match(/\[[\s\S]*\]/);
      if (arrayMatch) {
        cleaned = arrayMatch[0];
      }
    }

    // Estratégia 5: Limpar caracteres problemáticos comuns
    cleaned = cleaned
      .replace(/,\s*}/g, '}') // Remove vírgulas antes de }
      .replace(/,\s*]/g, ']') // Remove vírgulas antes de ]
      .replace(/\n\s*\n/g, '\n') // Remove linhas vazias duplas
      .replace(/\t/g, ' ') // Substitui tabs por espaços
      .replace(/\r/g, ''); // Remove carriage returns

    console.log(`✅ Tentativa ${attempt} - JSON limpo:`, cleaned.substring(0, 100) + '...');
    return cleaned;
  } catch (error) {
    console.error(`❌ Erro na tentativa ${attempt} ao limpar JSON do Gemini:`, error);
    return text;
  }
}

// Função para tentar múltiplas estratégias de parsing
function parseJSONWithFallback(text: string): any {
  const maxAttempts = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const cleaned = cleanGeminiJSON(text, attempt);

      // Tentar parse direto
      const parsed = JSON.parse(cleaned);
      console.log(`✅ JSON parseado com sucesso na tentativa ${attempt}`);
      return parsed;
    } catch (error) {
      lastError = error as Error;
      console.warn(`⚠️ Tentativa ${attempt} falhou:`, error);

      if (attempt < maxAttempts) {
        // Estratégias adicionais para tentativas subsequentes
        if (attempt === 2) {
          // Tentar com escape de caracteres especiais
          text = text.replace(/\\/g, '\\\\').replace(/"/g, '\\"');
        } else if (attempt === 3) {
          // Tentar extrair apenas números e strings básicas
          const simpleMatch = text.match(/\{[^{}]*\}/);
          if (simpleMatch) {
            text = simpleMatch[0];
          }
        }
      }
    }
  }

  // Se todas as tentativas falharam, logar detalhes completos
  console.error('❌ Todas as tentativas de parsing falharam');
  console.error('📄 Texto original completo:', text);
  console.error('📄 Último erro:', lastError?.message);

  throw new Error(`Falha ao fazer parse do JSON após ${maxAttempts} tentativas: ${lastError?.message}`);
}

// Função para validar e ajustar tamanho da resposta
function validateResponseLength(response: string, minWords: number = 15, maxWords: number = 60): string {
  const words = response.trim().split(/\s+/);
  const wordCount = words.length;

  console.log(`📏 Resposta da IA: ${wordCount} palavras (meta: ${minWords}-${maxWords})`);

  if (wordCount >= minWords && wordCount <= maxWords) {
    console.log('✅ Tamanho da resposta adequado');
    return response;
  }

  if (wordCount > maxWords) {
    // Truncar resposta se muito longa
    const truncated = words.slice(0, maxWords).join(' ');
    console.log(`✂️ Resposta truncada de ${wordCount} para ${maxWords} palavras`);
    return truncated + (truncated.endsWith('.') ? '' : '.');
  }

  if (wordCount < minWords) {
    console.log(`⚠️ Resposta muito curta (${wordCount} palavras), mantendo original`);
    return response;
  }

  return response;
}

export class GeminiAIService {
  private ai: GoogleGenerativeAI | null = null;
  private model: any = null;
  private isInitialized: boolean = false;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;

    if (!apiKey) {
      console.warn('⚠️  GEMINI_API_KEY não configurada. Serviço de IA desabilitado.');
      this.isInitialized = false;
      return;
    }

    try {
      this.ai = new GoogleGenerativeAI(apiKey);
      this.model = this.ai.getGenerativeModel({ model: "gemini-1.5-flash" });
      this.isInitialized = true;
      console.log('✅ Gemini AI inicializado com sucesso');
    } catch (error) {
      console.error('❌ Erro ao inicializar Gemini AI:', error);
      this.isInitialized = false;
    }
  }

  public async generateContent(prompt: string): Promise<string> {
    if (!this.isInitialized || !this.model) {
      throw new Error('Gemini AI não está inicializado');
    }

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text() || '';
    } catch (error) {
      console.error('Erro ao gerar conteúdo:', error);
      throw error;
    }
  }

  private async getConversationContext(contact: IContact, limit: number = 10) {
    // Contexto de conversa seria implementado com Supabase
    // Por enquanto, retornar array vazio
    console.log('📝 Contexto de conversa não implementado para Supabase');
    return [];
  }

  private formatContext(messages: IMessage[]) {
    return messages.map(msg => ({
      role: msg.fromMe ? 'assistant' : 'user',
      content: msg.content
    }));
  }

  public async generateResponse(contact: IContact, message: string) {
    try {
      const context = await this.getConversationContext(contact);
      const formattedContext = this.formatContext(context);

      const prompt = `
        Você é "Rafaela", assistente virtual inspirada na Vereadora Rafaela de Parnamirim - fonoaudióloga, cristã e defensora da inclusão.

        PERSONALIDADE RAFAELA:
        🧡 Tom maternal e acolhedor como uma irmã mais velha
        ✊🏾 Lutadora incansável pelo bem-estar das gestantes
        🙏🏽 Fé cristã que traz esperança e força
        💪🏽 "Seguimos firmes juntas" - linguagem de união e força

        ESTILO DE COMUNICAÇÃO:
        - Use "minha querida", "nossa gente", "juntas somos mais fortes"
        - Emoji principal: 🧡 (quando apropriado)
        - Referências à fé: "Deus abençoa", "com fé e esperança"
        - Linguagem de ação: "seguimos firmes", "caminhando juntas"
        - Tom inclusivo e acessível

        Contexto da gestante:
        - Nome: ${contact.name}
        - Telefone: ${contact.phone}
        - Gênero do bebê: ${contact.babyGender === 'male' ? 'Menino' : contact.babyGender === 'female' ? 'Menina' : 'Não informado'}

        Histórico da conversa:
        ${formattedContext.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

        Nova mensagem da gestante: ${message}

        DIRETRIZES (15-60 palavras):
        1. Tom maternal da Rafaela: "minha querida", "nossa família"
        2. Use o nome da gestante com carinho
        3. Mencione o bebê com gênero correto se conhecido
        4. Inclua elementos de fé quando apropriado
        5. Linguagem de força e união: "seguimos firmes", "juntas"
        6. Seja empática como a Vereadora Rafaela
        7. Sugira médico se necessário
        8. Termine com encorajamento

        RESPOSTA NO ESTILO RAFAELA (15-60 palavras):`;

      const rawResponse = await this.generateContent(prompt);

      // Validar e ajustar tamanho da resposta (15-60 palavras)
      const response = validateResponseLength(rawResponse, 15, 60);

      // Análise de sentimento e necessidades
      const sentimentAnalysis = await this.analyzeSentiment(message, contact);

      const result = {
        response,
        sentiment: sentimentAnalysis.sentiment,
        needs: sentimentAnalysis.needs,
        suggestions: sentimentAnalysis.suggestions
      };

      // Emitir eventos para webhooks
      // aiEventMiddleware seria implementado com ID do Supabase
      console.log('📊 Event middleware não implementado para Supabase');

      return result;
    } catch (error) {
      console.error('Erro ao gerar resposta:', error);
      throw error;
    }
  }

  private async analyzeSentiment(message: string, contact: IContact) {
    const prompt = `
      Analise a seguinte mensagem de uma gestante e forneça uma análise detalhada em formato JSON:
      
      Contexto:
      - Nome: ${contact.name}
      - Estágio da gestação: Não informado
      
      Mensagem: ${message}
      
      Forneça uma análise com os seguintes campos:
      {
        "sentiment": {
          "type": "positive/negative/neutral",
          "score": 0.0 a 1.0,
          "emotions": ["lista de emoções identificadas"]
        },
        "needs": ["lista de necessidades identificadas"],
        "suggestions": ["sugestões de acompanhamento"],
        "priority": "alta/média/baixa",
        "medical_attention": true/false,
        "follow_up": ["ações de acompanhamento recomendadas"]
      }`;

    const result = await this.generateContent(prompt);

    try {
      // Usar função avançada de parsing com múltiplas tentativas
      return parseJSONWithFallback(result);
    } catch (parseError) {
      console.error('❌ Todas as tentativas de parse falharam:', parseError);
      console.error('📄 Resposta original do Gemini:', result);

      // Tentar análise manual básica antes do fallback
      const manualAnalysis = this.attemptManualAnalysis(result, message);
      if (manualAnalysis) {
        console.log('🔧 Análise manual bem-sucedida');
        return manualAnalysis;
      }

      // Retornar análise padrão apenas como último recurso
      console.warn('⚠️ Usando análise padrão como último recurso');
      return {
        sentiment: {
          type: 'neutral',
          score: 0.5,
          emotions: ['indefinido']
        },
        needs: ['análise automática falhou - revisar resposta da IA'],
        suggestions: ['verificar logs do sistema', 'revisar prompt da IA'],
        priority: 'média',
        medical_attention: false,
        follow_up: ['acompanhar evolução', 'revisar configuração da IA'],
        debug_info: {
          original_response: result.substring(0, 500),
          error: (parseError as any).message,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  // Método para tentar análise manual básica quando o JSON falha
  private attemptManualAnalysis(aiResponse: string, originalMessage: string): any | null {
    try {
      console.log('🔧 Tentando análise manual da resposta da IA');

      const response = aiResponse.toLowerCase();
      const message = originalMessage.toLowerCase();

      // Detectar sentimento básico por palavras-chave
      let sentimentType = 'neutral';
      let sentimentScore = 0.5;
      const emotions: string[] = [];

      // Palavras positivas
      const positiveWords = ['feliz', 'alegre', 'bem', 'ótimo', 'bom', 'tranquila', 'animada'];
      const negativeWords = ['triste', 'preocupada', 'dor', 'mal', 'ruim', 'medo', 'ansiedade'];
      const urgentWords = ['urgente', 'emergência', 'sangramento', 'dor forte', 'hospital'];

      if (positiveWords.some(word => message.includes(word))) {
        sentimentType = 'positive';
        sentimentScore = 0.7;
        emotions.push('alegria', 'tranquilidade');
      } else if (negativeWords.some(word => message.includes(word))) {
        sentimentType = 'negative';
        sentimentScore = 0.3;
        emotions.push('preocupação');
      }

      // Detectar necessidades básicas
      const needs: string[] = [];
      const suggestions: string[] = [];
      let priority = 'média';
      let medicalAttention = false;

      if (urgentWords.some(word => message.includes(word))) {
        needs.push('atendimento médico urgente');
        suggestions.push('entrar em contato com médico imediatamente');
        priority = 'alta';
        medicalAttention = true;
      } else if (message.includes('dúvida') || message.includes('pergunta')) {
        needs.push('esclarecimento');
        suggestions.push('fornecer informações educativas');
      } else {
        needs.push('acompanhamento de rotina');
        suggestions.push('manter contato regular');
      }

      return {
        sentiment: {
          type: sentimentType,
          score: sentimentScore,
          emotions
        },
        needs,
        suggestions,
        priority,
        medical_attention: medicalAttention,
        follow_up: ['acompanhar evolução'],
        analysis_method: 'manual_fallback'
      };
    } catch (error) {
      console.error('❌ Erro na análise manual:', error);
      return null;
    }
  }

  public async generateFollowUpMessage(contact: IContact) {
    const prompt = `
      Você é Rafaela, inspirada na Vereadora de Parnamirim. Gere uma mensagem de acompanhamento para uma gestante:

      PERSONALIDADE RAFAELA:
      🧡 Maternal, acolhedora e próxima da comunidade
      💪🏽 "Seguimos firmes juntas" - linguagem de força e união
      🙏🏽 Fé cristã que traz esperança

      Gestante:
      - Nome: ${contact.name}
      - Gênero do bebê: ${contact.babyGender === 'male' ? 'Menino' : contact.babyGender === 'female' ? 'Menina' : 'Não informado'}

      ESTILO RAFAELA (20-50 palavras):
      1. Use "minha querida", "nossa gente"
      2. Tom maternal: "como você está?"
      3. Mencione o bebê com carinho
      4. Inclua elementos de fé quando apropriado: "Deus abençoa"
      5. Linguagem de união: "seguimos juntas", "nossa família"
      6. Ofereça suporte: "estou aqui para você"
      7. Termine com força e esperança

      MENSAGEM NO ESTILO RAFAELA (20-50 palavras):`;

    const rawResult = await this.generateContent(prompt);

    // Validar e ajustar tamanho da mensagem de follow-up (20-50 palavras)
    const result = validateResponseLength(rawResult, 20, 50);
    return result;
  }
} 
