<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> de Login</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>Teste de Login Frontend</h1>
    
    <form id="loginForm">
        <div>
            <label>Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        <div>
            <label>Senha:</label>
            <input type="password" id="password" value="Admin123!@#" required>
        </div>
        <button type="submit">Fazer Login</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                console.log('🔧 Tentando fazer login...');
                console.log('📧 Email:', email);
                console.log('🔑 Senha:', password);
                console.log('🌐 URL:', 'http://localhost:3333/api/auth/login');
                
                const response = await axios.post('http://localhost:3333/api/auth/login', {
                    email: email,
                    password: password
                }, {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('✅ Login bem-sucedido!', response.data);
                
                resultDiv.innerHTML = `
                    <h2 style="color: green;">✅ Login Bem-sucedido!</h2>
                    <p><strong>Token:</strong> ${response.data.token.substring(0, 50)}...</p>
                    <p><strong>Usuário:</strong> ${response.data.user.name}</p>
                    <p><strong>Role:</strong> ${response.data.user.role}</p>
                    <p><strong>Email:</strong> ${response.data.user.email}</p>
                `;
                
            } catch (error) {
                console.error('❌ Erro no login:', error);
                
                resultDiv.innerHTML = `
                    <h2 style="color: red;">❌ Erro no Login</h2>
                    <p><strong>Status:</strong> ${error.response?.status || 'Desconhecido'}</p>
                    <p><strong>Mensagem:</strong> ${error.response?.data?.message || error.message}</p>
                    <p><strong>Detalhes:</strong> ${JSON.stringify(error.response?.data || {}, null, 2)}</p>
                `;
            }
        });
    </script>
</body>
</html>
